{"mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/tmp/mcp-data"], "env": {"NODE_ENV": "production"}}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"NODE_ENV": "production"}}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "your-brave-api-key-here"}}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "your-github-token-here"}}, "postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres"], "env": {"POSTGRES_CONNECTION_STRING": "postgresql://user:password@localhost:5432/database"}}, "sqlite": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sqlite", "/tmp/mcp-data/database.db"], "env": {"NODE_ENV": "production"}}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "env": {"NODE_ENV": "production"}}}}